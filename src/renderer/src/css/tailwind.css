@tailwind base;
@tailwind components;
@tailwind utilities;

/* Dark mode syntax highlighting override */
@media (prefers-color-scheme: dark) {
  .hljs {
    background: rgba(0, 0, 0, 0.3) !important;
    color: #e6edf3 !important;
  }

  .hljs-keyword,
  .hljs-selector-tag,
  .hljs-literal,
  .hljs-section,
  .hljs-link {
    color: #ff7b72 !important;
  }

  .hljs-string {
    color: #a5d6ff !important;
  }

  .hljs-title,
  .hljs-name,
  .hljs-type,
  .hljs-attribute,
  .hljs-symbol,
  .hljs-bullet,
  .hljs-built_in,
  .hljs-addition,
  .hljs-variable,
  .hljs-template-tag,
  .hljs-template-variable {
    color: #79c0ff !important;
  }

  .hljs-comment,
  .hljs-quote,
  .hljs-deletion,
  .hljs-meta {
    color: #8b949e !important;
  }

  .hljs-number {
    color: #79c0ff !important;
  }
}

/* Custom scrollbar styles for agent progress */
@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-thumb-gray-300 {
    scrollbar-color: rgb(***********) transparent;
  }

  .dark .scrollbar-thumb-gray-600 {
    scrollbar-color: rgb(75 85 99) transparent;
  }

  /* Webkit scrollbar styles */
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
  }

  .dark .scrollbar-thin::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgba(209, 213, 219, 0.8);
    border-radius: 3px;
    transition: background-color 0.2s ease;
  }

  .dark .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgba(75, 85, 99, 0.8);
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgba(156, 163, 175, 0.9);
  }

  .dark .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgba(107, 114, 128, 0.9);
  }

  /* Enhanced scrollbar for agent progress components */
  .scrollbar-thin::-webkit-scrollbar-corner {
    background: transparent;
  }
}
@tailwind variants;

/* Apple 2025 Liquid Glass System - Enhanced */
@layer utilities {
  /* Base Liquid Glass Effect - Apple 2025 Style */
  .liquid-glass {
    position: relative;
    isolation: isolate;
    background: rgba(255, 255, 255, 0.12);
    backdrop-filter: blur(16px) saturate(180%) contrast(1.05);
    -webkit-backdrop-filter: blur(16px) saturate(180%) contrast(1.05);
    border: 1px solid rgba(255, 255, 255, 0.25);
    box-shadow:
      0 8px 32px rgba(31, 38, 135, 0.15),
      0 2px 8px rgba(255, 255, 255, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.4),
      inset 0 -1px 0 rgba(255, 255, 255, 0.15);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .liquid-glass::before {
    content: "";
    position: absolute;
    inset: 0;
    z-index: -1;
    border-radius: inherit;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.05) 50%,
      rgba(255, 255, 255, 0.15) 100%
    );
    box-shadow: inset 0 0 20px -5px rgba(255, 255, 255, 0.6);
    pointer-events: none;
  }

  .dark .liquid-glass {
    background: rgba(0, 0, 0, 1);
    border: 1px solid rgba(255, 255, 255, 0.18);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.4),
      0 2px 8px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.25),
      inset 0 -1px 0 rgba(255, 255, 255, 0.08);
  }

  .dark .liquid-glass::before {
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.08) 0%,
      rgba(255, 255, 255, 0.03) 50%,
      rgba(255, 255, 255, 0.12) 100%
    );
    box-shadow: inset 0 0 20px -5px rgba(255, 255, 255, 0.3);
  }

  /* Liquid Glass with Shine Effect */
  .liquid-glass-shine::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.2) 0%,
      rgba(255, 255, 255, 0.05) 50%,
      rgba(255, 255, 255, 0.1) 100%
    );
    border-radius: inherit;
    pointer-events: none;
    opacity: 0.6;
    z-index: 1;
  }

  .dark .liquid-glass-shine::after {
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.15) 0%,
      rgba(255, 255, 255, 0.03) 50%,
      rgba(255, 255, 255, 0.08) 100%
    );
  }

  /* Liquid Glass Variants - Enhanced for better text contrast */
  .liquid-glass-subtle {
    background: rgba(255, 255, 255, 0.12);
    backdrop-filter: blur(8px) saturate(150%) brightness(0.95);
    -webkit-backdrop-filter: blur(8px) saturate(150%) brightness(0.95);
    border: 1px solid rgba(255, 255, 255, 0.18);
    box-shadow:
      0 4px 16px rgba(31, 38, 135, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  .dark .liquid-glass-subtle {
    background: rgba(0, 0, 0, 1);
    backdrop-filter: blur(8px) saturate(150%) brightness(1.1);
    -webkit-backdrop-filter: blur(8px) saturate(150%) brightness(1.1);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow:
      0 4px 16px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
  }

  /* Liquid Glass Strong - Enhanced for better text contrast */
  .liquid-glass-strong {
    background: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(16px) saturate(200%) brightness(0.92);
    -webkit-backdrop-filter: blur(16px) saturate(200%) brightness(0.92);
    border: 1px solid rgba(255, 255, 255, 0.35);
    box-shadow:
      0 12px 40px rgba(31, 38, 135, 0.2),
      inset 0 2px 0 rgba(255, 255, 255, 0.4),
      inset 0 -2px 0 rgba(255, 255, 255, 0.15);
  }

  .dark .liquid-glass-strong {
    background: rgba(0, 0, 0, 1);
    backdrop-filter: blur(16px) saturate(200%) brightness(1.15);
    -webkit-backdrop-filter: blur(16px) saturate(200%) brightness(1.15);
    border: 1px solid rgba(255, 255, 255, 0.25);
    box-shadow:
      0 12px 40px rgba(0, 0, 0, 0.4),
      inset 0 2px 0 rgba(255, 255, 255, 0.25),
      inset 0 -2px 0 rgba(255, 255, 255, 0.08);
  }

  /* Apple 2025 Liquid Glass Navigation */
  .liquid-glass-nav {
    position: relative;
    isolation: isolate;
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(20px) saturate(160%) brightness(1.02);
    -webkit-backdrop-filter: blur(20px) saturate(160%) brightness(1.02);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
      0 6px 24px rgba(31, 38, 135, 0.12),
      0 1px 4px rgba(255, 255, 255, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .liquid-glass-nav::before {
    content: "";
    position: absolute;
    inset: 0;
    z-index: -1;
    background: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0.12) 0%,
      rgba(255, 255, 255, 0.04) 100%
    );
    pointer-events: none;
  }

  .dark .liquid-glass-nav {
    background: rgba(255, 255, 255, 0.04);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow:
      0 6px 24px rgba(0, 0, 0, 0.3),
      0 1px 4px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  .dark .liquid-glass-nav::before {
    background: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0.08) 0%,
      rgba(255, 255, 255, 0.02) 100%
    );
  }

  /* Apple 2025 Liquid Glass Button States */
  .liquid-glass-button {
    position: relative;
    isolation: isolate;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(12px) saturate(170%) brightness(1.05);
    -webkit-backdrop-filter: blur(12px) saturate(170%) brightness(1.05);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow:
      0 4px 16px rgba(31, 38, 135, 0.15),
      0 1px 3px rgba(255, 255, 255, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .liquid-glass-button::before {
    content: "";
    position: absolute;
    inset: 0;
    z-index: -1;
    border-radius: inherit;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.2) 0%,
      rgba(255, 255, 255, 0.05) 50%,
      rgba(255, 255, 255, 0.15) 100%
    );
    opacity: 0.8;
    pointer-events: none;
  }

  .liquid-glass-button:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow:
      0 8px 24px rgba(31, 38, 135, 0.2),
      0 2px 6px rgba(255, 255, 255, 0.25),
      inset 0 1px 0 rgba(255, 255, 255, 0.5);
    transform: translateY(-1px) scale(1.02);
  }

  .liquid-glass-button:active {
    transform: translateY(0) scale(0.98);
    box-shadow:
      0 2px 8px rgba(31, 38, 135, 0.25),
      inset 0 2px 4px rgba(31, 38, 135, 0.1);
  }

  .dark .liquid-glass-button {
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
      0 4px 16px rgba(0, 0, 0, 0.3),
      0 1px 3px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }

  .dark .liquid-glass-button::before {
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.12) 0%,
      rgba(255, 255, 255, 0.03) 50%,
      rgba(255, 255, 255, 0.08) 100%
    );
  }

  .dark .liquid-glass-button:hover {
    background: rgba(255, 255, 255, 0.12);
    border-color: rgba(255, 255, 255, 0.25);
    box-shadow:
      0 8px 24px rgba(0, 0, 0, 0.4),
      0 2px 6px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.35);
  }

  .dark .liquid-glass-button:active {
    box-shadow:
      0 2px 8px rgba(0, 0, 0, 0.4),
      inset 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  /* Liquid Glass Input Fields */
  .liquid-glass-input {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(8px) saturate(140%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
      inset 0 2px 4px rgba(31, 38, 135, 0.1),
      0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .liquid-glass-input:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(59, 130, 246, 0.5);
    box-shadow:
      inset 0 2px 4px rgba(31, 38, 135, 0.15),
      0 0 0 3px rgba(59, 130, 246, 0.1),
      0 1px 0 rgba(255, 255, 255, 0.25);
  }

  .dark .liquid-glass-input {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.12);
    box-shadow:
      inset 0 2px 4px rgba(0, 0, 0, 0.2),
      0 1px 0 rgba(255, 255, 255, 0.15);
  }

  .dark .liquid-glass-input:focus {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(59, 130, 246, 0.4);
    box-shadow:
      inset 0 2px 4px rgba(0, 0, 0, 0.25),
      0 0 0 3px rgba(59, 130, 246, 0.15),
      0 1px 0 rgba(255, 255, 255, 0.2);
  }

  /* Liquid Glass Cards */
  .liquid-glass-card {
    background: rgba(255, 255, 255, 0.12);
    backdrop-filter: blur(14px) saturate(175%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
      0 8px 32px rgba(31, 38, 135, 0.12),
      inset 0 1px 0 rgba(255, 255, 255, 0.25),
      inset 0 -1px 0 rgba(255, 255, 255, 0.1);
  }

  .dark .liquid-glass-card {
    background: rgba(255, 255, 255, 0.06);
    border: 1px solid rgba(255, 255, 255, 0.12);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.18),
      inset 0 -1px 0 rgba(255, 255, 255, 0.05);
  }

  /* Liquid Glass Modal/Dialog */
  .liquid-glass-modal {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px) saturate(190%);
    border: 1px solid rgba(255, 255, 255, 0.25);
    box-shadow:
      0 16px 64px rgba(31, 38, 135, 0.2),
      inset 0 2px 0 rgba(255, 255, 255, 0.35),
      inset 0 -2px 0 rgba(255, 255, 255, 0.15);
  }

  .dark .liquid-glass-modal {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.18);
    box-shadow:
      0 16px 64px rgba(0, 0, 0, 0.5),
      inset 0 2px 0 rgba(255, 255, 255, 0.25),
      inset 0 -2px 0 rgba(255, 255, 255, 0.08);
  }

  /* Liquid Glass Panel */
  .liquid-glass-panel {
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(16px) saturate(160%);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow:
      0 10px 40px rgba(31, 38, 135, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  .dark .liquid-glass-panel {
    background: rgba(255, 255, 255, 0.04);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow:
      0 10px 40px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
  }

  /* Accessibility and Reduced Motion */
  @media (prefers-reduced-motion: reduce) {
    .liquid-glass,
    .liquid-glass-subtle,
    .liquid-glass-strong,
    .liquid-glass-nav,
    .liquid-glass-button,
    .liquid-glass-input,
    .liquid-glass-card,
    .liquid-glass-modal,
    .liquid-glass-panel {
      transition: none;
    }

    .liquid-glass-button:hover {
      transform: none;
    }
  }

  /* High Contrast Mode Support */
  @media (prefers-contrast: high) {
    .liquid-glass,
    .liquid-glass-subtle,
    .liquid-glass-strong,
    .liquid-glass-nav,
    .liquid-glass-button,
    .liquid-glass-input,
    .liquid-glass-card,
    .liquid-glass-modal,
    .liquid-glass-panel {
      backdrop-filter: none;
      background: rgba(255, 255, 255, 0.9);
      border: 2px solid rgba(0, 0, 0, 0.8);
    }

    .dark .liquid-glass,
    .dark .liquid-glass-subtle,
    .dark .liquid-glass-strong,
    .dark .liquid-glass-nav,
    .dark .liquid-glass-button,
    .dark .liquid-glass-input,
    .dark .liquid-glass-card,
    .dark .liquid-glass-modal,
    .dark .liquid-glass-panel {
      background: rgba(0, 0, 0, 1);
      border: 2px solid rgba(255, 255, 255, 0.8);
    }
  }

  /* Browser Fallbacks */
  @supports not (backdrop-filter: blur(1px)) {
    .liquid-glass,
    .liquid-glass-subtle,
    .liquid-glass-strong,
    .liquid-glass-nav,
    .liquid-glass-button,
    .liquid-glass-input,
    .liquid-glass-card,
    .liquid-glass-modal,
    .liquid-glass-panel {
      background: rgba(255, 255, 255, 0.85);
    }

    .dark .liquid-glass,
    .dark .liquid-glass-subtle,
    .dark .liquid-glass-strong,
    .dark .liquid-glass-nav,
    .dark .liquid-glass-button,
    .dark .liquid-glass-input,
    .dark .liquid-glass-card,
    .dark .liquid-glass-modal,
    .dark .liquid-glass-panel {
      background: rgba(0, 0, 0, 1);
    }
  }

  /* Apple 2025 Advanced Liquid Glass with SVG Displacement */
  .liquid-glass-distortion {
    backdrop-filter: blur(16px) saturate(180%) contrast(1.05)
      url("./assets/apple-2025-liquid-glass.svg#apple-2025-liquid-glass");
    -webkit-backdrop-filter: blur(16px) saturate(180%) contrast(1.05)
      url("./assets/apple-2025-liquid-glass.svg#apple-2025-liquid-glass");
  }

  .liquid-glass-nav-distortion {
    backdrop-filter: blur(20px) saturate(160%) brightness(1.02)
      url("./assets/apple-2025-liquid-glass.svg#apple-2025-floating");
    -webkit-backdrop-filter: blur(20px) saturate(160%) brightness(1.02)
      url("./assets/apple-2025-liquid-glass.svg#apple-2025-floating");
  }

  .liquid-glass-button-distortion {
    backdrop-filter: blur(12px) saturate(170%) brightness(1.05)
      url("./assets/apple-2025-liquid-glass.svg#apple-2025-interactive");
    -webkit-backdrop-filter: blur(12px) saturate(170%) brightness(1.05)
      url("./assets/apple-2025-liquid-glass.svg#apple-2025-interactive");
  }

  .liquid-glass-modal-distortion {
    backdrop-filter: blur(24px) saturate(190%) brightness(1.03)
      url("./assets/apple-2025-liquid-glass.svg#apple-2025-floating");
    -webkit-backdrop-filter: blur(24px) saturate(190%) brightness(1.03)
      url("./assets/apple-2025-liquid-glass.svg#apple-2025-floating");
  }

  .liquid-glass-card-distortion {
    backdrop-filter: blur(14px) saturate(175%) contrast(1.03)
      url("./assets/apple-2025-liquid-glass.svg#apple-2025-liquid-glass");
    -webkit-backdrop-filter: blur(14px) saturate(175%) contrast(1.03)
      url("./assets/apple-2025-liquid-glass.svg#apple-2025-liquid-glass");
  }

  .liquid-glass-input-distortion {
    backdrop-filter: blur(8px) saturate(140%) brightness(1.02)
      url("./assets/apple-2025-liquid-glass.svg#apple-2025-ripple");
    -webkit-backdrop-filter: blur(8px) saturate(140%) brightness(1.02)
      url("./assets/apple-2025-liquid-glass.svg#apple-2025-ripple");
  }

  /* Fallback for browsers that don't support SVG filters in backdrop-filter */
  @supports not (backdrop-filter: url("#test")) {
    .liquid-glass-distortion,
    .liquid-glass-nav-distortion,
    .liquid-glass-button-distortion,
    .liquid-glass-modal-distortion,
    .liquid-glass-card-distortion,
    .liquid-glass-input-distortion {
      backdrop-filter: blur(12px) saturate(180%);
    }
  }

  /* Utility classes for quick application */
  .glass-bg-light {
    background: rgba(255, 255, 255, 0.15);
  }

  .glass-bg-medium {
    background: rgba(255, 255, 255, 0.2);
  }

  .glass-bg-strong {
    background: rgba(255, 255, 255, 0.25);
  }

  .dark .glass-bg-light {
    background: rgba(255, 255, 255, 0.08);
  }

  .dark .glass-bg-medium {
    background: rgba(255, 255, 255, 0.12);
  }

  .dark .glass-bg-strong {
    background: rgba(255, 255, 255, 0.16);
  }

  .glass-border {
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .dark .glass-border {
    border: 1px solid rgba(255, 255, 255, 0.15);
  }

  .glass-shadow {
    box-shadow:
      0 8px 32px rgba(31, 38, 135, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.25);
  }

  .dark .glass-shadow {
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.18);
  }

  .glass-border-r {
    border-right: 1px solid rgba(255, 255, 255, 0.2);
  }

  .dark .glass-border-r {
    border-right: 1px solid rgba(255, 255, 255, 0.15);
  }

  .glass-border-b {
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  }

  .dark .glass-border-b {
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
  }

  /* Apple 2025 Liquid Glass - Interactive Effects */
  .liquid-glass-interactive {
    position: relative;
    isolation: isolate;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(16px) saturate(180%) brightness(1.03);
    -webkit-backdrop-filter: blur(16px) saturate(180%) brightness(1.03);
    border: 1px solid rgba(255, 255, 255, 0.25);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
  }

  .liquid-glass-interactive::before {
    content: "";
    position: absolute;
    inset: 0;
    z-index: -1;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.15) 0%,
      rgba(255, 255, 255, 0.05) 50%,
      rgba(255, 255, 255, 0.2) 100%
    );
    opacity: 0;
    transition: opacity 0.4s ease;
    pointer-events: none;
  }

  .liquid-glass-interactive::after {
    content: "";
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
      45deg,
      transparent 30%,
      rgba(255, 255, 255, 0.1) 50%,
      transparent 70%
    );
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
    transition: transform 0.6s ease;
    pointer-events: none;
  }

  .liquid-glass-interactive:hover::before {
    opacity: 1;
  }

  .liquid-glass-interactive:hover::after {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }

  .liquid-glass-interactive:hover {
    backdrop-filter: blur(20px) saturate(200%) brightness(1.05);
    -webkit-backdrop-filter: blur(20px) saturate(200%) brightness(1.05);
    border-color: rgba(255, 255, 255, 0.35);
    box-shadow:
      0 12px 40px rgba(31, 38, 135, 0.2),
      0 4px 12px rgba(255, 255, 255, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
  }

  .dark .liquid-glass-interactive {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.18);
  }

  .dark .liquid-glass-interactive::before {
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.03) 50%,
      rgba(255, 255, 255, 0.12) 100%
    );
  }

  .dark .liquid-glass-interactive:hover {
    border-color: rgba(255, 255, 255, 0.25);
    box-shadow:
      0 12px 40px rgba(0, 0, 0, 0.4),
      0 4px 12px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }

  /* Apple 2025 Liquid Glass - Floating Effect */
  .liquid-glass-floating {
    position: relative;
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(24px) saturate(160%) brightness(1.02);
    -webkit-backdrop-filter: blur(24px) saturate(160%) brightness(1.02);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
      0 20px 60px rgba(31, 38, 135, 0.15),
      0 8px 24px rgba(255, 255, 255, 0.1),
      inset 0 2px 0 rgba(255, 255, 255, 0.3),
      inset 0 -2px 0 rgba(255, 255, 255, 0.1);
    animation: liquid-glass-float 6s ease-in-out infinite;
  }

  @keyframes liquid-glass-float {
    0%,
    100% {
      transform: translateY(0px) rotate(0deg);
      box-shadow:
        0 20px 60px rgba(31, 38, 135, 0.15),
        0 8px 24px rgba(255, 255, 255, 0.1),
        inset 0 2px 0 rgba(255, 255, 255, 0.3);
    }
    50% {
      transform: translateY(-10px) rotate(1deg);
      box-shadow:
        0 30px 80px rgba(31, 38, 135, 0.2),
        0 12px 32px rgba(255, 255, 255, 0.15),
        inset 0 2px 0 rgba(255, 255, 255, 0.4);
    }
  }

  .dark .liquid-glass-floating {
    background: rgba(255, 255, 255, 0.04);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow:
      0 20px 60px rgba(0, 0, 0, 0.3),
      0 8px 24px rgba(0, 0, 0, 0.2),
      inset 0 2px 0 rgba(255, 255, 255, 0.2),
      inset 0 -2px 0 rgba(255, 255, 255, 0.05);
  }

  /* Apple 2025 Liquid Glass - Ripple Effect */
  .liquid-glass-ripple {
    position: relative;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.12);
    backdrop-filter: blur(18px) saturate(175%) brightness(1.04);
    -webkit-backdrop-filter: blur(18px) saturate(175%) brightness(1.04);
    border: 1px solid rgba(255, 255, 255, 0.25);
  }

  .liquid-glass-ripple::before {
    content: "";
    position: absolute;
    inset: 0;
    background: radial-gradient(
      circle at var(--ripple-x, 50%) var(--ripple-y, 50%),
      rgba(255, 255, 255, 0.3) 0%,
      rgba(255, 255, 255, 0.1) 30%,
      transparent 70%
    );
    opacity: 0;
    transform: scale(0);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
  }

  .liquid-glass-ripple:active::before {
    opacity: 1;
    transform: scale(2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .dark .liquid-glass-ripple {
    background: rgba(255, 255, 255, 0.06);
    border: 1px solid rgba(255, 255, 255, 0.18);
  }

  .dark .liquid-glass-ripple::before {
    background: radial-gradient(
      circle at var(--ripple-x, 50%) var(--ripple-y, 50%),
      rgba(255, 255, 255, 0.2) 0%,
      rgba(255, 255, 255, 0.08) 30%,
      transparent 70%
    );
  }

  /* Apple 2025 Liquid Glass - Enhanced Utilities */
  .glass-blur-light {
    backdrop-filter: blur(8px) saturate(150%);
    -webkit-backdrop-filter: blur(8px) saturate(150%);
  }

  .glass-blur-medium {
    backdrop-filter: blur(16px) saturate(180%) brightness(1.02);
    -webkit-backdrop-filter: blur(16px) saturate(180%) brightness(1.02);
  }

  .glass-blur-heavy {
    backdrop-filter: blur(24px) saturate(200%) brightness(1.05) contrast(1.03);
    -webkit-backdrop-filter: blur(24px) saturate(200%) brightness(1.05)
      contrast(1.03);
  }

  .glass-shine {
    position: relative;
    overflow: hidden;
  }

  .glass-shine::after {
    content: "";
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
      45deg,
      transparent 30%,
      rgba(255, 255, 255, 0.1) 50%,
      transparent 70%
    );
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
    transition: transform 0.6s ease;
    pointer-events: none;
    opacity: 0;
  }

  .glass-shine:hover::after {
    transform: translateX(100%) translateY(100%) rotate(45deg);
    opacity: 1;
  }

  /* Apple 2025 Color Variants */
  .glass-tint-blue {
    background: rgba(59, 130, 246, 0.1);
    border-color: rgba(59, 130, 246, 0.2);
  }

  .dark .glass-tint-blue {
    background: rgba(59, 130, 246, 0.05);
    border-color: rgba(59, 130, 246, 0.15);
  }

  .glass-tint-purple {
    background: rgba(147, 51, 234, 0.1);
    border-color: rgba(147, 51, 234, 0.2);
  }

  .dark .glass-tint-purple {
    background: rgba(147, 51, 234, 0.05);
    border-color: rgba(147, 51, 234, 0.15);
  }

  .glass-tint-green {
    background: rgba(34, 197, 94, 0.1);
    border-color: rgba(34, 197, 94, 0.2);
  }

  .dark .glass-tint-green {
    background: rgba(34, 197, 94, 0.05);
    border-color: rgba(34, 197, 94, 0.15);
  }

  .glass-tint-amber {
    background: rgba(245, 158, 11, 0.1);
    border-color: rgba(245, 158, 11, 0.2);
  }

  .dark .glass-tint-amber {
    background: rgba(245, 158, 11, 0.05);
    border-color: rgba(245, 158, 11, 0.15);
  }

  /* Text contrast utilities for glass backgrounds */
  .glass-text-contrast {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }

  .dark .glass-text-contrast {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
  }

  .glass-text-strong {
    color: rgba(0, 0, 0, 0.9);
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
  }

  .dark .glass-text-strong {
    color: rgba(255, 255, 255, 0.95);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
  }

  /* Ensure proper text color inheritance on glass elements */
  .liquid-glass *,
  .liquid-glass-strong *,
  .liquid-glass-subtle * {
    color: inherit;
  }

  .liquid-glass .text-muted-foreground,
  .liquid-glass-strong .text-muted-foreground,
  .liquid-glass-subtle .text-muted-foreground {
    opacity: 0.8;
  }

  .dark .liquid-glass .text-muted-foreground,
  .dark .liquid-glass-strong .text-muted-foreground,
  .dark .liquid-glass-subtle .text-muted-foreground {
    opacity: 0.7;
  }
}

:root {
  --background: 0 0% 100%;
  --foreground: 0 0% 3.9%;
  --card: 0 0% 100%;
  --card-foreground: 0 0% 3.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 0 0% 3.9%;
  --primary: 0 0% 9%;
  --primary-foreground: 0 0% 98%;
  --secondary: 0 0% 96.1%;
  --secondary-foreground: 0 0% 9%;
  --muted: 0 0% 96.1%;
  --muted-foreground: 0 0% 45.1%;
  --accent: 0 0% 96.1%;
  --accent-foreground: 0 0% 9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 0% 98%;
  --border: 0 0% 95%;
  --input: 0 0% 89.8%;
  --ring: 217 91% 60%;
  --chart-1: 12 76% 61%;
  --chart-2: 173 58% 39%;
  --chart-3: 197 37% 24%;
  --chart-4: 43 74% 66%;
  --chart-5: 27 87% 67%;
  --radius: 0.5rem;
}

.dark {
  --background: 0 0% 0%;
  --foreground: 0 0% 99%;
  --card: 0 0% 3.9%;
  --card-foreground: 0 0% 98%;
  --popover: 0 0% 3.9%;
  --popover-foreground: 0 0% 98%;
  --primary: 0 0% 98%;
  --primary-foreground: 0 0% 9%;
  --secondary: 0 0% 14.9%;
  --secondary-foreground: 0 0% 98%;
  --muted: 0 0% 14.9%;
  --muted-foreground: 0 0% 63.9%;
  --accent: 0 0% 14.9%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --border: 0 0% 14.9%;
  --input: 0 0% 14.9%;
  --ring: 221 83% 53%;
  --chart-1: 220 70% 50%;
  --chart-2: 160 60% 45%;
  --chart-3: 30 80% 55%;
  --chart-4: 280 65% 60%;
  --chart-5: 340 75% 55%;
}

* {
  @apply select-none border-border;
}
body {
  @apply text-foreground antialiased;
}
html {
  color-scheme: light;
}

html.dark {
  color-scheme: dark;
}

html,
body {
  @apply bg-transparent;
}

.app-drag-region {
  -webkit-app-region: drag;
}

.app-no-drag-region {
  -webkit-app-region: no-drag;
}

button,
a[role="button"] {
  @apply cursor-default;
}

button,
a,
input,
textarea,
select {
  -webkit-app-region: no-drag;
}

/* Line clamp utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
