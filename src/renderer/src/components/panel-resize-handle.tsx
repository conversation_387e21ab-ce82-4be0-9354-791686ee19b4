import React, { useState, useRef, useEffect } from "react"
import { cn } from "@renderer/lib/utils"
import { tipcClient } from "@renderer/lib/tipc-client"

interface PanelResizeHandleProps {
  className?: string
  disabled?: boolean
  position: "right" | "bottom" | "bottom-right"
}

export function PanelResizeHandle({
  className,
  disabled = false,
  position,
}: PanelResizeHandleProps) {
  const [isResizing, setIsResizing] = useState(false)
  const [resizeStart, setResizeStart] = useState<{
    x: number
    y: number
    windowWidth: number
    windowHeight: number
    windowX: number
    windowY: number
  } | null>(null)
  const handleRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!isResizing || disabled) return

    const handleMouseMove = (e: MouseEvent) => {
      if (!resizeStart) return

      // Calculate new size based on mouse movement from initial position
      const deltaX = e.screenX - resizeStart.x
      const deltaY = e.screenY - resizeStart.y

      let newWidth = resizeStart.windowWidth
      let newHeight = resizeStart.windowHeight
      let newX = resizeStart.windowX
      let newY = resizeStart.windowY

      // Apply constraints based on position
      if (position === "right" || position === "bottom-right") {
        newWidth = Math.max(200, resizeStart.windowWidth + deltaX)
      }
      if (position === "bottom" || position === "bottom-right") {
        newHeight = Math.max(40, resizeStart.windowHeight + deltaY)
      }

      // Update panel size and position via IPC
      tipcClient.updatePanelSize({
        width: newWidth,
        height: newHeight,
        x: newX,
        y: newY,
      })
    }

    const handleMouseUp = (e: MouseEvent) => {
      if (!resizeStart) return

      // Calculate final size
      const deltaX = e.screenX - resizeStart.x
      const deltaY = e.screenY - resizeStart.y

      let finalWidth = resizeStart.windowWidth
      let finalHeight = resizeStart.windowHeight

      if (position === "right" || position === "bottom-right") {
        finalWidth = Math.max(200, resizeStart.windowWidth + deltaX)
      }
      if (position === "bottom" || position === "bottom-right") {
        finalHeight = Math.max(40, resizeStart.windowHeight + deltaY)
      }

      // Save the final size
      tipcClient.savePanelSize({
        width: finalWidth,
        height: finalHeight,
      })

      setIsResizing(false)
      setResizeStart(null)
      document.body.style.cursor = ""
    }

    document.addEventListener("mousemove", handleMouseMove)
    document.addEventListener("mouseup", handleMouseUp)

    // Set cursor style based on position
    const cursor = position === "right" ? "ew-resize" : 
                  position === "bottom" ? "ns-resize" : 
                  "nwse-resize"
    document.body.style.cursor = cursor

    return () => {
      document.removeEventListener("mousemove", handleMouseMove)
      document.removeEventListener("mouseup", handleMouseUp)
      document.body.style.cursor = ""
    }
  }, [isResizing, resizeStart, disabled, position])

  const handleMouseDown = async (e: React.MouseEvent) => {
    if (disabled) return

    e.preventDefault()
    e.stopPropagation()

    // Get current window bounds
    const windowBounds = await tipcClient.getPanelBounds()

    setIsResizing(true)
    setResizeStart({
      x: e.screenX,
      y: e.screenY,
      windowWidth: windowBounds.width,
      windowHeight: windowBounds.height,
      windowX: windowBounds.x,
      windowY: windowBounds.y,
    })
  }

  const getCursorClass = () => {
    if (disabled) return "cursor-default"
    if (isResizing) {
      return position === "right" ? "cursor-ew-resize" : 
             position === "bottom" ? "cursor-ns-resize" : 
             "cursor-nwse-resize"
    }
    return position === "right" ? "cursor-ew-resize hover:bg-white/10" : 
           position === "bottom" ? "cursor-ns-resize hover:bg-white/10" : 
           "cursor-nwse-resize hover:bg-white/10"
  }

  const getPositionClasses = () => {
    switch (position) {
      case "right":
        return "absolute right-0 top-0 h-full w-2 cursor-ew-resize"
      case "bottom":
        return "absolute bottom-0 left-0 w-full h-2 cursor-ns-resize"
      case "bottom-right":
        return "absolute bottom-0 right-0 w-4 h-4 cursor-nwse-resize"
      default:
        return ""
    }
  }

  return (
    <div
      ref={handleRef}
      className={cn(
        "transition-colors duration-200 z-50",
        getPositionClasses(),
        getCursorClass(),
        disabled && "opacity-30",
        className,
      )}
      onMouseDown={handleMouseDown}
      style={{
        userSelect: "none",
      }}
    >
      {/* Visual indicator for bottom-right corner */}
      {position === "bottom-right" && !disabled && (
        <div className="absolute bottom-0 right-0 w-3 h-3">
          <div className="absolute bottom-0 right-0 w-2 h-0.5 bg-current opacity-60" />
          <div className="absolute bottom-0.5 right-0 w-0.5 h-2 bg-current opacity-60" />
        </div>
      )}
    </div>
  )
}
