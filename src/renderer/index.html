<!doctype html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>SpeakMCP</title>
    <!-- https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP -->
    <meta
      http-equiv="Content-Security-Policy"
      content="default-src 'self' blob: assets:; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:"
    />
    <script>
      function toggleThemeClass(darkMode) {
        const root = document.documentElement
        if (darkMode) {
          root.classList.add("dark")
        } else {
          root.classList.remove("dark")
        }
      }

      function getThemePreference() {
        try {
          // Try to get theme preference from config
          const configData = localStorage.getItem("theme-preference")
          if (configData) {
            return configData
          }
        } catch (e) {
          // Fallback to system preference
        }
        return "system"
      }

      function applyTheme(themePreference = null) {
        const preference = themePreference || getThemePreference()

        if (preference === "light") {
          toggleThemeClass(false)
        } else if (preference === "dark") {
          toggleThemeClass(true)
        } else {
          // System preference
          const darkMode = window.matchMedia(
            "(prefers-color-scheme: dark)",
          ).matches
          toggleThemeClass(darkMode)
        }
      }

      function initThemeClass() {
        applyTheme()
      }

      // Listen for system theme changes only if using system preference
      window
        .matchMedia("(prefers-color-scheme: dark)")
        .addEventListener("change", (e) => {
          const preference = getThemePreference()
          if (preference === "system") {
            toggleThemeClass(e.matches)
          }
        })

      // Listen for theme preference changes from the app
      window.addEventListener("theme-preference-changed", (e) => {
        const newPreference = e.detail
        localStorage.setItem("theme-preference", newPreference)
        applyTheme(newPreference)
      })

      initThemeClass()
    </script>
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
